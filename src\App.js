import { useState } from 'react';
import "./App.css";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  CardContent,
  TextField,
  Typography,
  Button,
  Box,
  IconButton
} from "@mui/material";
import AddIcon from '@mui/icons-material/Add';
import DeleteIcon from '@mui/icons-material/Delete';

function App() {
  // متغيرات الحالة (State Variables)
  const [title, setTitle] = useState(''); // عنوان الملاحظة
  const [content, setContent] = useState(''); // محتوى الملاحظة
  const [notes, setNotes] = useState([]); // قائمة الملاحظات

  // دالة إضافة ملاحظة جديدة
  const handleAddNote = (e) => {
    e.preventDefault(); // منع إعادة تحميل الصفحة

    // التحقق من أن العنوان والمحتوى غير فارغين
    if (title.trim() && content.trim()) {
      // إنشاء ملاحظة جديدة
      const newNote = {
        id: Date.now(), // معرف فريد باستخدام الوقت الحالي
        title: title.trim(),
        content: content.trim(),
        createdAt: new Date() // تاريخ الإنشاء
      };

      // إضافة الملاحظة الجديدة إلى القائمة
      setNotes([...notes, newNote]);

      // مسح الحقول بعد الإضافة
      setTitle('');
      setContent('');
    }
  };

  // دالة حذف ملاحظة
  const handleDeleteNote = (noteId) => {
    // تصفية الملاحظات وإزالة الملاحظة المحددة
    setNotes(notes.filter(note => note.id !== noteId));
  };

  return (
   
      <Container maxWidth="md" sx={{ py: 4 }}>
        {/* نموذج إضافة ملاحظة */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              إضافة ملاحظة جديدة
            </Typography>
            <Box component="form" onSubmit={handleAddNote}>
              <TextField
                fullWidth
                label="عنوان الملاحظة"
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                margin="normal"
                required
              />
              <TextField
                fullWidth
                label="محتوى الملاحظة"
                value={content}
                onChange={(e) => setContent(e.target.value)}
                margin="normal"
                multiline
                rows={4}
                required
              />
              <Button
                type="submit"
                variant="contained"
                startIcon={<AddIcon />}
                sx={{ mt: 2 }}
              >
                إضافة ملاحظة
              </Button>
            </Box>
          </CardContent>
        </Card>

        {/* قائمة الملاحظات */}
        <Box>
          <Typography variant="h6" gutterBottom>
            الملاحظات ({notes.length})
          </Typography>
          
          {notes.length === 0 ? (
            <Box textAlign="center" py={4}>
              <Typography variant="h6" color="text.secondary">
                لا توجد ملاحظات حتى الآن
              </Typography>
              <Typography variant="body2" color="text.secondary">
                قم بإضافة ملاحظة جديدة لتبدأ
              </Typography>
            </Box>
          ) : (
            notes.map((note) => (
              <Card key={note.id} sx={{ mb: 2, '&:hover': { boxShadow: 3 } }}>
                <CardContent>
                  <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                    <Box flex={1}>
                      <Typography variant="h6" component="div" gutterBottom>
                        {note.title}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {note.content}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        تاريخ الإنشاء: {note.createdAt.toLocaleDateString('ar-SA')}
                      </Typography>
                    </Box>
                    <IconButton
                      color="error"
                      onClick={() => handleDeleteNote(note.id)}
                      size="small"
                      sx={{ ml: 1 }}
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Box>
                </CardContent>
              </Card>
            ))
          )}
        </Box>
      </Container>

   
  );
}

export default App;
