.App {
  text-align: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  background-color: #282c34;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: calc(10px + 2vmin);
  color: white;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* دعم RTL للنصوص العربية */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .MuiTextField-root {
  text-align: right;
}

[dir="rtl"] .MuiInputBase-input {
  text-align: right;
}

/* تحسين الخطوط العربية */
body {
  font-family: 'Segoe UI', '<PERSON><PERSON><PERSON>', 'Aria<PERSON>', sans-serif;
}
